// WebSocket 纯技术服务层 - 只负责连接管理和消息处理
import { API_CONFIG, APP_CONFIG } from '../../config'
import { simpleim } from '../../proto/message.js'
import type {
  IWebSocketService,
  WebSocketServiceEvents,
  WebSocketConfig,
  SendMessage,
  ReceivedMessage,
  ConnectionHealth,
  HeartbeatStatus
} from './types'
import { DEFAULT_WEBSOCKET_CONFIG, MessageType } from './types'

export class WebSocketService implements IWebSocketService {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private token: string | null = null

  // 定时器
  private heartbeatInterval: number | null = null
  private reconnectTimeout: number | null = null
  private connectionTimeout: number | null = null
  private heartbeatTimeout: number | null = null

  // 心跳状态
  private lastHeartbeatSentTime = 0
  private lastHeartbeatReceivedTime = 0
  private missedHeartbeats = 0

  // 事件监听器
  private events: Partial<WebSocketServiceEvents> = {}

  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      ...DEFAULT_WEBSOCKET_CONFIG,
      url: API_CONFIG.WS_URL,
      ...config
    }
  }

  // 事件监听器管理
  on<K extends keyof WebSocketServiceEvents>(event: K, callback: WebSocketServiceEvents[K]): void {
    this.events[event] = callback
  }

  off<K extends keyof WebSocketServiceEvents>(event: K): void {
    delete this.events[event]
  }

  private emit<K extends keyof WebSocketServiceEvents>(
    event: K,
    ...args: Parameters<NonNullable<WebSocketServiceEvents[K]>>
  ): void {
    const callback = this.events[event]
    if (callback) {
      ;(callback as any)(...args)
    }
  }

  // 连接WebSocket
  async connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经连接，直接返回
        if (this.isConnected()) {
          resolve()
          return
        }

        // 设置token
        this.token = token || localStorage.getItem(APP_CONFIG.TOKEN_KEY)
        if (!this.token) {
          reject(new Error('未找到认证token'))
          return
        }

        // 清理之前的连接
        this.cleanup()

        // 创建WebSocket连接
        const wsUrl = `${this.config.url}?token=${this.token}`
        this.ws = new WebSocket(wsUrl)
        this.ws.binaryType = 'arraybuffer'

        // 设置连接超时
        this.connectionTimeout = window.setTimeout(() => {
          console.error('WebSocket连接超时')
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close()
            reject(new Error('连接超时'))
          }
        }, this.config.connectionTimeout)

        // 连接成功
        this.ws.onopen = () => {
          console.log('WebSocket连接成功')
          this.clearConnectionTimeout()
          this.startHeartbeat()
          this.emit('onOpen')
          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.stopHeartbeat()
          this.emit('onClose', event)
        }

        // 连接错误
        this.ws.onerror = (event) => {
          console.error('WebSocket连接错误:', event)
          this.clearConnectionTimeout()
          this.emit('onError', event)
          reject(new Error('WebSocket连接失败'))
        }
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect(): void {
    this.cleanup()
    if (this.ws) {
      this.ws.close(1000, '主动断开')
      this.ws = null
    }
  }

  // 发送消息
  send(message: SendMessage): boolean {
    if (!this.isConnected() || !this.ws) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      // 使用protobuf编码消息
      const encodedMessage = simpleim.IMMessage.encode(message).finish()
      this.ws.send(encodedMessage)

      // 如果是心跳消息，记录发送时间
      if (message.type === MessageType.HEARTBEAT) {
        this.lastHeartbeatSentTime = Date.now()
        this.startHeartbeatTimeout()
      }

      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  // 检查连接状态
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  // 获取连接健康状态
  getConnectionHealth(): ConnectionHealth {
    const now = Date.now()
    const timeSinceLastHeartbeat = now - this.lastHeartbeatReceivedTime
    const isConnected = this.isConnected()
    const isHealthy = isConnected && this.missedHeartbeats < this.config.maxMissedHeartbeats

    let status: ConnectionHealth['status'] = 'unknown'
    if (!isConnected) {
      status = 'disconnected'
    } else if (this.missedHeartbeats >= this.config.maxMissedHeartbeats) {
      status = 'unhealthy'
    } else if (this.missedHeartbeats > 0) {
      status = 'warning'
    } else {
      status = 'healthy'
    }

    return {
      isConnected,
      isHealthy,
      timeSinceLastHeartbeat,
      missedHeartbeats: this.missedHeartbeats,
      status
    }
  }

  // 获取心跳状态
  getHeartbeatStatus(): HeartbeatStatus {
    return {
      lastSent: this.lastHeartbeatSentTime,
      lastReceived: this.lastHeartbeatReceivedTime,
      missedCount: this.missedHeartbeats,
      maxMissed: this.config.maxMissedHeartbeats,
      isTimeout: this.heartbeatTimeout !== null
    }
  }

  // 处理接收到的消息
  private handleMessage(data: ArrayBuffer): void {
    try {
      // 使用protobuf解码消息
      const message = simpleim.IMMessage.decode(new Uint8Array(data)) as ReceivedMessage

      console.log('WebSocketService - 接收到消息:', {
        type: message.type,
        messageId: message.messageId,
        timestamp: message.timestamp,
        hasTextMessage: !!message.textMessage,
        rawMessage: message
      })

      // 检查消息类型是否有效
      if (typeof message.type === 'undefined' || message.type === null) {
        console.warn('WebSocketService - 消息类型未定义:', message)
        return
      }

      // 处理心跳响应
      if (message.type === MessageType.HEARTBEAT) {
        this.handleHeartbeatResponse()
        return
      }

      // 发射消息事件
      this.emit('onMessage', message)
    } catch (error) {
      console.error('解析消息失败:', error, {
        dataLength: data.byteLength,
        dataPreview: new Uint8Array(data.slice(0, Math.min(50, data.byteLength)))
      })
    }
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.stopHeartbeat()
    console.log('🫀 [WebSocketService] 开始心跳机制:', {
      intervalMs: this.config.heartbeatInterval,
      timeoutMs: this.config.heartbeatTimeout,
      maxMissedHeartbeats: this.config.maxMissedHeartbeats,
      startTime: new Date().toISOString()
    })

    this.heartbeatInterval = window.setInterval(() => {
      this.sendHeartbeat()
    }, this.config.heartbeatInterval)
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      console.log('🛑 [WebSocketService] 停止心跳机制:', {
        stopTime: new Date().toISOString(),
        lastSentTime: this.lastHeartbeatSentTime,
        lastReceivedTime: this.lastHeartbeatReceivedTime,
        missedHeartbeats: this.missedHeartbeats
      })
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    this.clearHeartbeatTimeout()
  }

  // 发送心跳
  private sendHeartbeat(): void {
    const heartbeat: SendMessage = {
      type: MessageType.HEARTBEAT,
      timestamp: Date.now()
    }

    console.log('🫀 [WebSocketService] 发送心跳消息:', {
      type: heartbeat.type,
      timestamp: heartbeat.timestamp,
      timestampDate: new Date(heartbeat.timestamp).toISOString(),
      messageTypeValue: MessageType.HEARTBEAT,
      rawMessage: heartbeat
    })

    const success = this.send(heartbeat)
    if (!success) {
      console.warn('🫀 [WebSocketService] 心跳消息发送失败')
    }
  }

  // 处理心跳响应
  private handleHeartbeatResponse(): void {
    const now = Date.now()
    const responseTime = now - this.lastHeartbeatSentTime

    console.log('💓 [WebSocketService] 接收到心跳响应:', {
      receivedTime: now,
      receivedTimeDate: new Date(now).toISOString(),
      lastSentTime: this.lastHeartbeatSentTime,
      lastSentTimeDate: new Date(this.lastHeartbeatSentTime).toISOString(),
      responseTime: responseTime + 'ms',
      missedHeartbeatsReset: this.missedHeartbeats,
      connectionHealthy: true
    })

    this.lastHeartbeatReceivedTime = now
    this.missedHeartbeats = 0
    this.clearHeartbeatTimeout()
  }

  // 开始心跳超时检测
  private startHeartbeatTimeout(): void {
    this.clearHeartbeatTimeout()
    this.heartbeatTimeout = window.setTimeout(() => {
      this.missedHeartbeats++
      console.warn(`💔 [WebSocketService] 心跳超时:`, {
        missedCount: this.missedHeartbeats,
        maxAllowed: this.config.maxMissedHeartbeats,
        lastSentTime: this.lastHeartbeatSentTime,
        lastSentTimeDate: new Date(this.lastHeartbeatSentTime).toISOString(),
        lastReceivedTime: this.lastHeartbeatReceivedTime,
        lastReceivedTimeDate: this.lastHeartbeatReceivedTime
          ? new Date(this.lastHeartbeatReceivedTime).toISOString()
          : 'Never',
        timeoutMs: this.config.heartbeatTimeout,
        willDisconnect: this.missedHeartbeats >= this.config.maxMissedHeartbeats
      })

      if (this.missedHeartbeats >= this.config.maxMissedHeartbeats) {
        console.error('💀 [WebSocketService] 心跳超时次数过多，主动断开连接')
        this.disconnect()
      }
    }, this.config.heartbeatTimeout)
  }

  // 清除心跳超时
  private clearHeartbeatTimeout(): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  // 清除连接超时
  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }
  }

  // 清理资源
  private cleanup(): void {
    this.stopHeartbeat()
    this.clearConnectionTimeout()
    this.clearHeartbeatTimeout()

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
  }
}
